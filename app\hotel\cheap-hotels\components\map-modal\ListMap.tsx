import { Hotel } from '@/models/hotel/list-page.model';
import React from 'react'
import MapHotelList from './components/MapHotelList';
import FilterHotel from '../filter-hotel/FilterHotel';
import InteractiveMap from '@/app/components/map-box-map/InteractiveMap';
import { HotelFilterData } from '@/models/hotel/filter.model';
import { GeoCode } from '@/models/hotel/search-page.model';

export interface ListMapProps {
  cordinates: GeoCode | undefined;
  isLoading: boolean;
  hotels: Hotel[];
  masterHotelList: Hotel[];
  filterData: HotelFilterData | undefined;
  handleFilterChange: (filterData: HotelFilterData | undefined) => void;
  handleViewDetails: (hotelId: number) => void;
}



function ListMap({ isLoading , hotels , filterData , handleFilterChange , masterHotelList , cordinates , handleViewDetails }:ListMapProps) {

  return (
    <div className="w-full h-full flex flex-col md:flex-row bg-gray-100">
      <div className="w-full md:w-1/2 h-[50vh] md:h-full flex flex-col md:flex-row">
        <div className="w-full md:w-[35%] bg-white shadow-md">
          <FilterHotel filterData={filterData} handleChange={handleFilterChange} />
        </div>
        <div className="w-full md:w-[65%] bg-white shadow-md overflow-y-auto">
          <MapHotelList handleViewDetails={handleViewDetails} hotels={hotels} isLoading={isLoading} />
        </div>
      </div>
      <div className="w-full md:w-1/2 h-[50vh] md:h-full bg-white shadow-md">
        <InteractiveMap cordinates={cordinates} hotels={masterHotelList} handleButtonClick={handleViewDetails} />
      </div>
    </div>
  )
}

export default ListMap