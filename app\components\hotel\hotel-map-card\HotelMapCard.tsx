"use client";
import React, { useCallback, useState } from "react";

import Image from "next/image";
import { useTranslation } from "@/app/hooks/useTranslation";
import { showToast } from "@/app/components/utilities/SonnerToasterCustom";
import { useCommonContext } from "@/app/contexts/commonContext";
import { BadgePercent, Heart, Star, MapPin, CheckCircle, CreditCard, PawPrint, Car, Hotel as HotelIcon, Building2, Home, TreePine, Bed, Castle, Info, Coffee, CheckCircle2, Briefcase, Dog, Baby, Sparkles, Dumbbell, Clock, Bus, ConciergeBell, GlassWater, UtensilsCrossed, AirVent, ParkingSquare, Waves, Wifi } from "lucide-react";
import { getAmenityDetails } from "@/app/components/utilities/amenities";
import { Amenity, Hotel } from "@/models/hotel/list-page.model";
import { getConvertedCurrency } from "@/helpers/hotel/currency-helper";
import PriceShimmer from "@/app/hotel/cheap-hotels/components/hotel-card/PriceShimmer";

interface MapHotelCardProps {
  hotel: Hotel;
  handleClick: () => void;
  isAllowClick?: boolean;
  isSearchCompleted?: boolean;
  isPricingAvailable?: boolean;
}

const iconComponentMap: { [key: string]: React.ElementType } = {
    Wifi: Wifi,
    Waves: Waves,
    ParkingSquare: ParkingSquare,
    AirVent: AirVent,
    UtensilsCrossed: UtensilsCrossed,
    GlassWater: GlassWater,
    Bellboy: ConciergeBell,
    Bus: Bus,
    Clock: Clock,
    Dumbbell: Dumbbell,
    Sparkles: Sparkles,
    Baby: Baby,
    Dog: Dog,
    Briefcase: Briefcase,
    CheckCircle2: CheckCircle2,
    Coffee: Coffee,
    Info: Info,
    CheckCircle: CheckCircle,
    PawPrint: PawPrint,
    CreditCard: CreditCard
};

function MapHotelCard({
  hotel,
  handleClick,
  isAllowClick = false,
  isSearchCompleted = false,
  isPricingAvailable = false,
}: MapHotelCardProps) {
  const { t } = useTranslation();
  const [isHeartActive, setIsHeartActive] = useState<boolean>(false);
  const { selectedCurrency } = useCommonContext();

  // Function to get processed special amenities using the amenities utility
  const getProcessedSpecialAmenities = (specialAmenities: string[]) => {
    if (!specialAmenities || !Array.isArray(specialAmenities)) return [];
    return specialAmenities
      .filter(amenity => amenity && typeof amenity === 'string' && amenity.trim().length > 0)
      .map(amenity => getAmenityDetails(amenity.trim()))
      .slice(0, 4);
  };

  const getFilteredAmenities = (amenities: Amenity[], topOfferings: string[], specialAmenities: string[]) => {
    if (!amenities) return [];
    const specialAmenitiesSet = new Set(specialAmenities);
    const filtered = amenities.filter(amenity => {
      const amenityName = amenity.name || "";
      return !specialAmenitiesSet.has(amenityName);
    });
    return filtered;
  };

  const handlaSave = useCallback(() => {
    if (isHeartActive) {
      setIsHeartActive(false);
      showToast("Removed from favorites", "error");
    } else {
      setIsHeartActive(true);
      showToast("Added to favorites", "default");
    }
  }, [isHeartActive]);

  // Function to get rating badge color based on rating value
  const getRatingBadgeColor = (rating: string | number) => {
    const numRating = typeof rating === 'string' ? parseFloat(rating) : rating;
    if (numRating >= 9.0) return "bg-green-600";
    if (numRating >= 8.0) return "bg-blue-600";
    if (numRating >= 7.0) return "bg-orange-500";
    if (numRating >= 6.0) return "bg-red-500";
    return "bg-gray-500";
  };

  // Function to get accommodation type styling and icon
  const getAccommodationTypeStyle = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('hotel')) return { bgColor: "bg-indigo-100", textColor: "text-indigo-800", icon: HotelIcon };
    if (lowerType.includes('resort')) return { bgColor: "bg-emerald-100", textColor: "text-emerald-800", icon: TreePine };
    if (lowerType.includes('apartment') || lowerType.includes('flat')) return { bgColor: "bg-purple-100", textColor: "text-purple-800", icon: Building2 };
    if (lowerType.includes('villa') || lowerType.includes('house') || lowerType.includes('cottage')) return { bgColor: "bg-green-100", textColor: "text-green-800", icon: Home };
    if (lowerType.includes('hostel') || lowerType.includes('lodge') || lowerType.includes('dormitory')) return { bgColor: "bg-orange-100", textColor: "text-orange-800", icon: Bed };
    if (lowerType.includes('guest') || lowerType.includes('b&b') || lowerType.includes('bnb') || lowerType.includes('homestay')) return { bgColor: "bg-pink-100", textColor: "text-pink-800", icon: Heart };
    if (lowerType.includes('palace') || lowerType.includes('castle') || lowerType.includes('heritage')) return { bgColor: "bg-amber-100", textColor: "text-amber-800", icon: Castle };
    if (lowerType.includes('motel') || lowerType.includes('inn')) return { bgColor: "bg-blue-100", textColor: "text-blue-800", icon: Car };
    return { bgColor: "bg-gray-100", textColor: "text-gray-800", icon: HotelIcon };
  };

  return (
    <div
      onClick={isAllowClick ? handleClick : undefined}
      // --- CHANGE IS HERE ---
      className={`relative bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden w-full mb-6 ${isAllowClick ? 'cursor-pointer' : 'cursor-default'}`}
    >
      <div className="flex flex-col min-h-auto">
        {/* Hotel Image */}
        <div className="relative w-full h-48">
          {hotel?.imageInfoList?.length > 0 ? (
            <Image
              alt="hotel image"
              src={hotel.imageInfoList[0].url}
              fill
              className="object-cover object-center"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              unoptimized
              placeholder="blur"
              blurDataURL="/img/no-image-placeholder.webp"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-500 text-sm font-medium">
              {t("hotel.card.noImageFound")}
            </div>
          )}
          <button
            onClick={(e) => {
              handlaSave();
              e.stopPropagation();
            }}
            className="absolute top-3 right-3 bg-white bg-opacity-70 p-2 rounded-full cursor-pointer"
          >
            <Heart size={20} className="text-red-500" fill={isHeartActive ? "#ef4444" : "none"} />
          </button>
        </div>

        {/* Hotel Information */}
        <div className="p-4 w-full border-b border-gray-200">
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-1">
              <h2 className="text-xl font-bold text-gray-800">{hotel?.name}</h2>
              {(() => {
                const accommodationType = hotel?.hotelType || "Hotel";
                const style = getAccommodationTypeStyle(accommodationType);
                const IconComponent = style.icon;
                return (
                  <span className={`px-2 py-0.5 ${style.bgColor} ${style.textColor} text-xs font-medium rounded whitespace-nowrap`}>
                    <IconComponent size={12} className="mr-1 inline" />
                    {accommodationType}
                  </span>
                );
              })()}
            </div>
            <div className="flex items-center mt-1">
              <div className="flex text-yellow-400 mr-2">
                {Array.from({ length: Math.max(0, hotel?.starRating || 0) }).map((_, index) => (
                  <Star key={index} size={16} fill="#fbbf24" className="text-yellow-400" />
                ))}
              </div>
            </div>
          </div>
          <div className="mb-4">
            <div className="flex items-center text-sm text-gray-600 mb-1">
              <MapPin size={14} className="mr-2 text-gray-500" />
              <span>{`${hotel?.address}, ${hotel.city}` || ""}</span>
            </div>
            {hotel?.distanceFromSearchedEntity && (
              <div className="flex items-center text-sm text-gray-500">
                <MapPin size={14} className="mr-2 text-gray-400" />
                <span>{hotel.distanceFromSearchedEntity}</span>
              </div>
            )}
          </div>
          {hotel?.specialAmenities && hotel.specialAmenities.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {getProcessedSpecialAmenities(hotel.specialAmenities).map((amenityDetail, index) => {
                const IconComponent = iconComponentMap[amenityDetail.iconName] || Info;
                return (
                  <span key={index} className={`px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap flex items-center ${
                    amenityDetail.key === 'pay_at_hotel' ? "bg-blue-100 text-blue-800" :
                    amenityDetail.key === 'free_cancellation' ? "bg-green-100 text-green-800" :
                    amenityDetail.key === 'pets' ? "bg-orange-100 text-orange-800" :
                    "bg-purple-100 text-purple-800"
                  }`}>
                    <IconComponent size={12} className="mr-1 inline" />
                    {amenityDetail.displayName}
                  </span>
                );
              })}
            </div>
          )}
          <div className="flex flex-wrap gap-x-4 gap-y-1 mb-2">
            {getFilteredAmenities(hotel?.amenities || [], hotel?.topOfferings || [], hotel?.specialAmenities || [])?.slice(0, 5).map((amenity, index) => {
              const amenityDetails = getAmenityDetails(amenity.name);
              const IconComponent = iconComponentMap[amenityDetails.iconName] || Info;
              return (
                <div key={index} className="flex items-center text-sm text-gray-700 min-w-0">
                  <IconComponent size={16} className="w-4 h-4 mr-2 flex-shrink-0 text-gray-500" />
                  <span className="whitespace-nowrap">{amenityDetails.displayName}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Pricing & Actions */}
        <div className="p-4 w-full flex flex-col justify-between">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-2">
              <div className="text-left">
                <div className="text-sm font-semibold text-gray-800">{hotel?.userRatingCategory || "Good"}</div>
                {hotel?.userRatingCount && hotel.userRatingCount > 0 && (
                  <div className="text-xs text-gray-500">{hotel.userRatingCount} reviews</div>
                )}
              </div>
              <div className={`${getRatingBadgeColor(hotel?.userRating || "0")} text-white text-sm font-bold px-2 py-1 rounded min-w-[40px] text-center`}>
                {hotel?.userRating || "0"}
              </div>
            </div>
            <div className="text-right">
              {isPricingAvailable ? (
                <>
                  {hotel?.fareDetail?.displayedBaseFare && hotel?.fareDetail?.displayedBaseFare > hotel?.fareDetail?.totalPrice && (
                    <div className="flex items-center justify-end mb-1">
                      <span className="line-through text-gray-500 text-sm mr-2">
                        {getConvertedCurrency(hotel.fareDetail.displayedBaseFare, selectedCurrency)}
                      </span>
                      <span className="bg-red-100 text-red-700 text-xs font-medium px-2 py-0.5 rounded">
                        {Math.round(((hotel.fareDetail.displayedBaseFare - hotel.fareDetail.totalPrice) / hotel.fareDetail.displayedBaseFare) * 100)}% OFF
                      </span>
                    </div>
                  )}
                  <div className="mb-1">
                    <span className="text-2xl font-bold text-gray-800">
                      {getConvertedCurrency(hotel?.fareDetail?.totalPrice, selectedCurrency)}
                    </span>
                    <span className="text-sm text-gray-600">/night</span>
                  </div>
                </>
              ) : (
                <PriceShimmer layout="grid" />
              )}
              {isSearchCompleted && (
                <div className="text-sm text-gray-500 mb-4">
                  <div>+{getConvertedCurrency(hotel?.taxesAndCharges, selectedCurrency)} taxes & charges</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Promotional Banner */}
      {(hotel?.fareDetail?.offer_title || hotel?.fareDetail?.offer_description) && (
        <div className="bg-yellow-50 border-t border-yellow-100 p-2">
          <div className="flex items-center text-xs text-yellow-800">
            <BadgePercent size={14} className="mr-2 flex-shrink-0" />
            <div className="flex flex-wrap items-center gap-1">
              <span className="font-medium">
                {hotel.fareDetail.offer_title || hotel.fareDetail.offer_description || ""}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MapHotelCard;