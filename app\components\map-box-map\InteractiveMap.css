/* InteractiveMap.css */

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Price markers on the map */
.price-marker {
  background: #ffffff;
  border: 2px solid #007bff;
  border-radius: 20px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #007bff;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 40px;
  text-align: center;
}

.price-marker:hover {
  background: #007bff;
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Selected marker for detail page */
.selected-marker {
  background: #D9534F;
  border: 2px solid #ffffff;
  border-radius: 50%;
  padding: 8px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(217, 83, 79, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(217, 83, 79, 0.5);
}

/* Hotel popup styles */
.hotel-popup {
  max-width: 280px;
  padding: 0;
}

.hotel-popup .mapboxgl-popup-content {
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.hotel-popup .mapboxgl-popup-close-button {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 16px;
  font-weight: bold;
  top: 8px;
  right: 8px;
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotel-popup .mapboxgl-popup-close-button:hover {
  background: rgba(0, 0, 0, 0.7);
}

.popup-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.hotel-popup h3 {
  margin: 12px 16px 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.popup-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 16px 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.rating-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.popup-price {
  margin: 0 16px 8px;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
}

.per-night {
  font-size: 12px;
  color: #666;
}

.popup-info {
  margin: 0 16px 12px;
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.popup-button {
  width: calc(100% - 32px);
  margin: 0 16px 16px;
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.popup-button:hover {
  background: #0056b3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .price-marker {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .hotel-popup {
    max-width: 240px;
  }
  
  .popup-image {
    height: 100px;
  }
  
  .hotel-popup h3 {
    font-size: 14px;
    margin: 10px 12px 6px;
  }
  
  .popup-rating,
  .popup-price,
  .popup-info {
    margin-left: 12px;
    margin-right: 12px;
  }
  
  .popup-button {
    width: calc(100% - 24px);
    margin: 0 12px 12px;
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* Map loading state */
.map-container.loading {
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-container.loading::before {
  content: "Loading map...";
  color: #666;
  font-size: 14px;
}

/* Accessibility improvements */
.price-marker:focus,
.selected-marker:focus,
.popup-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Animation for marker appearance */
@keyframes markerAppear {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.price-marker,
.selected-marker {
  animation: markerAppear 0.3s ease-out;
}
