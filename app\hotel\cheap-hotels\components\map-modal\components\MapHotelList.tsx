import { Hotel } from '@/models/hotel/list-page.model';
import React from 'react'
import MapHotelCard from '@/app/components/hotel/hotel-map-card/HotelMapCard';

interface props{
    hotels: Hotel[];
    handleViewDetails: (hotelId: number) => void;
    isLoading: boolean;
}

function MapHotelList({hotels , isLoading , handleViewDetails}:props) {
  return (
    <div className='flex flex-col'>
        {hotels.map((item,index)=>(
          <MapHotelCard 
            key={index}
            hotel={item}
            handleClick={()=>handleViewDetails(item.hotelId)}
            isSearchCompleted={isLoading}
            isPricingAvailable={isLoading}
          />  
        ))}
    </div>
  )
}

export default MapHotelList