// Booking Data Transformation Utilities
// Transforms API response data to UI-friendly format

import { 
  UserBookingItem, 
  UserBookingsApiResponse, 
  TransformedBookingItem
} from "@/models/user/user-bookings.model";

/**
 * Transform API booking status to UI status
 */
export const transformBookingStatus = (
  apiStatus: string, 
  paymentStatus: string
): "confirmed" | "pending" | "cancelled" | "failed" => {
  const status = apiStatus.toUpperCase();
  const payment = paymentStatus.toUpperCase();

  // If payment failed or booking failed, mark as failed
  if (status === 'FAILED' || payment === 'FAILED') {
    return 'failed';
  }

  // If cancelled
  if (status === 'CANCELLED') {
    return 'cancelled';
  }

  // If confirmed and paid
  if (status === 'CONFIRMED' && payment === 'PAID') {
    return 'confirmed';
  }

  // If completed
  if (status === 'COMPLETED') {
    return 'confirmed';
  }

  // Log unexpected status values
  console.log(`Unexpected booking status: ${status}, payment status: ${payment}`);

  // Default to pending for other cases
  return 'pending';
};

/**
 * Format date range for display
 */
export const formatDateRange = (startDate: string, endDate: string): string => {
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };

    const startFormatted = start.toLocaleDateString('en-US', options);
    const endFormatted = end.toLocaleDateString('en-US', options);

    return `${startFormatted} - ${endFormatted}`;
  } catch (error) {
    console.error('Error formatting date range:', error);
    return 'Invalid Date Range';
  }
};

/**
 * Format single date for display
 */
export const formatSingleDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };

    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Extract hotel amenities/facilities from room data
 */
export const extractAmenities = (booking: UserBookingItem): string[] => {
  try {
    const hotelBooking = booking.hotel_booking?.provider_response?.hotelBooking;
    if (!hotelBooking) return [];

    const amenities = new Set<string>();

    // Add hotel facilities
    if (hotelBooking.hotel?.facilities) {
      hotelBooking.hotel.facilities.forEach(facility => {
        if (facility.name) {
          amenities.add(facility.name);
        }
      });
    }

    // Add room facilities
    if (hotelBooking.rooms) {
      hotelBooking.rooms.forEach(room => {
        if (room.facilities) {
          room.facilities.forEach(facility => {
            if (facility.name) {
              amenities.add(facility.name);
            }
          });
        }
      });
    }

    return Array.from(amenities).slice(0, 5); // Limit to 5 amenities for UI
  } catch (error) {
    console.error('Error extracting amenities:', error);
    return [];
  }
};

/**
 * Get guest names from booking
 */
export const extractGuestNames = (booking: UserBookingItem): string[] => {
  try {
    const hotelBooking = booking.hotel_booking?.provider_response?.hotelBooking;
    if (!hotelBooking) return [];

    return hotelBooking.guests || [];
  } catch (error) {
    console.error('Error extracting guest names:', error);
    return [];
  }
};

/**
 * Get room details from booking
 */
export const extractRoomDetails = (booking: UserBookingItem) => {
  try {
    const hotelBooking = booking.hotel_booking?.provider_response?.hotelBooking;
    if (!hotelBooking?.rooms) return [];

    return hotelBooking.rooms.map(room => ({
      name: room.name || 'Standard Room',
      description: room.description || '',
      facilities: room.facilities?.map(f => f.name).filter(Boolean) || []
    }));
  } catch (error) {
    console.error('Error extracting room details:', error);
    return [];
  }
};

/**
 * Get hotel location string
 */
export const getHotelLocation = (booking: UserBookingItem): string => {
  try {
    const hotel = booking.hotel_booking?.provider_response?.hotelBooking?.hotel;
    if (!hotel) return 'Unknown Location';

    const address = hotel.contact?.address;
    if (address) {
      const city = address.city?.name || '';
      const country = address.country?.name || '';
      
      if (city && country) {
        return `${city}, ${country}`;
      } else if (city) {
        return city;
      } else if (country) {
        return country;
      }
    }

    return 'Unknown Location';
  } catch (error) {
    console.error('Error getting hotel location:', error);
    return 'Unknown Location';
  }
};

/**
 * Get total booking price
 */
export const getTotalPrice = (booking: UserBookingItem): number => {
  try {
    const hotelBooking = booking.hotel_booking?.provider_response?.hotelBooking;
    if (!hotelBooking) return 0;

    // Try to get total rate from the booking
    if (hotelBooking.totalRate && hotelBooking.totalRate > 0) {
      return hotelBooking.totalRate;
    }

    // Fallback: sum up rates
    if (hotelBooking.rates && hotelBooking.rates.length > 0) {
      const totalFromRates = hotelBooking.rates.reduce((total, rate) => total + (rate.totalRate || 0), 0);
      if (totalFromRates > 0) return totalFromRates;
    }

    // Another fallback: check supplier rates
    if (hotelBooking.supplierRates && hotelBooking.supplierRates.length > 0) {
      const totalFromSupplierRates = hotelBooking.supplierRates.reduce((total, rate) => total + (rate.totalRate || 0), 0);
      if (totalFromSupplierRates > 0) return totalFromSupplierRates;
    }

    return 0;
  } catch (error) {
    console.error('Error getting total price:', error);
    return 0;
  }
};

/**
 * Get booking currency
 */
export const getBookingCurrency = (booking: UserBookingItem): string => {
  try {
    const hotelBooking = booking.hotel_booking?.provider_response?.hotelBooking;
    if (!hotelBooking) return 'INR';

    // Try to get currency from rates
    if (hotelBooking.rates && hotelBooking.rates.length > 0) {
      const currency = hotelBooking.rates[0]?.currency;
      if (currency && !currency.includes('not available')) {
        return currency;
      }
    }

    // Fallback: try supplier rates
    if (hotelBooking.supplierRates && hotelBooking.supplierRates.length > 0) {
      const currency = hotelBooking.supplierRates[0]?.currency;
      if (currency && !currency.includes('not available')) {
        return currency;
      }
    }

    return 'INR'; // Default fallback
  } catch (error) {
    console.error('Error getting booking currency:', error);
    return 'INR';
  }
};

/**
 * Transform a single booking item from API to UI format
 */
export const transformBookingItem = (apiBooking: UserBookingItem): TransformedBookingItem => {
  const hotelBooking = apiBooking.hotel_booking?.provider_response?.hotelBooking;
  const hotel = hotelBooking?.hotel;

  return {
    id: apiBooking.id.toString(),
    type: "hotel",
    name: hotel?.name || 'Unknown Hotel',
    location: getHotelLocation(apiBooking),
    date: formatDateRange(
      hotelBooking?.tripStartDate || apiBooking.created_at,
      hotelBooking?.tripEndDate || apiBooking.created_at
    ),
    status: transformBookingStatus(apiBooking.status, apiBooking.payment_status),
    price: getTotalPrice(apiBooking),
    currency: getBookingCurrency(apiBooking),
    reference: apiBooking.booking_reference,
    amenities: extractAmenities(apiBooking),
    checkIn: hotelBooking?.tripStartDate || '',
    checkOut: hotelBooking?.tripEndDate || '',
    guests: extractGuestNames(apiBooking),
    hotelImage: hotel?.heroImage || '',
    paymentStatus: apiBooking.payment_status,
    bookingDate: formatSingleDate(apiBooking.created_at),
    providerName: hotelBooking?.providerName || 'Unknown Provider',
    roomDetails: extractRoomDetails(apiBooking)
  };
};

/**
 * Transform array of API bookings to UI format
 */
export const transformBookingsArray = (apiBookings: UserBookingsApiResponse): TransformedBookingItem[] => {
  if (!Array.isArray(apiBookings)) {
    console.error('Invalid bookings data: expected array');
    return [];
  }

  console.log(`🔍 Processing ${apiBookings.length} bookings...`);

  const transformed = apiBookings.map(transformBookingItem);

  // Summary of what we got
  console.log('📊 BOOKING SUMMARY:');
  transformed.forEach(b => {
    console.log(`  ID ${b.id}: ${b.name} | ${b.location} | ${b.currency} ${b.price} | ${b.status} | ${b.checkIn}`);
  });

  return transformed;
};

/**
 * Filter bookings by status for UI tabs
 */
export const filterBookingsByStatus = (
  bookings: TransformedBookingItem[],
  statusFilter: "Upcoming" | "Completed" | "Cancelled"
): TransformedBookingItem[] => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  switch (statusFilter) {
    case "Upcoming":
      const upcomingResults = bookings.filter(booking => {
        const isNotCancelled = booking.status !== 'cancelled' && booking.status !== 'failed';

        if (booking.checkIn) {
          try {
            const checkInDate = new Date(booking.checkIn);
            // Set check-in date to midnight for proper comparison
            checkInDate.setHours(0, 0, 0, 0);
            // Include same-day bookings (>= today) and future bookings
            const isTodayOrFuture = checkInDate >= today;

            console.log(`🔍 Booking ${booking.id}: checkIn=${booking.checkIn}, checkInDate=${checkInDate.toISOString()}, today=${today.toISOString()}, isTodayOrFuture=${isTodayOrFuture}`);

            return isNotCancelled && isTodayOrFuture;
          } catch (error) {
            console.error(`Error parsing check-in date for booking ${booking.id}:`, error);
            return isNotCancelled;
          }
        }

        return isNotCancelled;
      });

      console.log(`📊 UPCOMING: ${upcomingResults.length}/${bookings.length} bookings`);
      return upcomingResults;

    case "Completed":
      const completedResults = bookings.filter(booking => {
        const isCompleted = booking.status === 'confirmed';

        if (booking.checkOut) {
          try {
            const checkOutDate = new Date(booking.checkOut);
            // Set checkout date to midnight for proper comparison
            checkOutDate.setHours(0, 0, 0, 0);
            const isPast = checkOutDate < today;

            console.log(`🔍 Booking ${booking.id}: checkOut=${booking.checkOut}, checkOutDate=${checkOutDate.toISOString()}, today=${today.toISOString()}, isPast=${isPast}`);

            return isCompleted && isPast;
          } catch (error) {
            console.error(`Error parsing check-out date for booking ${booking.id}:`, error);
            return false;
          }
        }

        return false;
      });

      console.log(`📊 COMPLETED: ${completedResults.length}/${bookings.length} bookings`);
      return completedResults;

    case "Cancelled":
      const cancelledResults = bookings.filter(booking =>
        booking.status === 'cancelled' || booking.status === 'failed'
      );

      console.log(`📊 CANCELLED: ${cancelledResults.length}/${bookings.length} bookings`);
      return cancelledResults;

    default:
      return bookings;
  }
};

/**
 * Sort bookings by different criteria
 */
export const sortBookings = (
  bookings: TransformedBookingItem[], 
  sortBy: "default" | "date" | "price"
): TransformedBookingItem[] => {
  if (sortBy === "default") {
    return bookings;
  }

  return [...bookings].sort((a, b) => {
    if (sortBy === "date") {
      const dateA = new Date(a.checkIn || a.bookingDate);
      const dateB = new Date(b.checkIn || b.bookingDate);
      return dateA.getTime() - dateB.getTime();
    } else if (sortBy === "price") {
      return a.price - b.price;
    }
    return 0;
  });
};

/**
 * Search bookings by query string
 */
export const searchBookings = (
  bookings: TransformedBookingItem[],
  query: string
): TransformedBookingItem[] => {
  if (!query.trim()) {
    return bookings;
  }

  const searchTerm = query.toLowerCase().trim();

  return bookings.filter(booking => {
    try {
      // Search in hotel name
      if (booking.name?.toLowerCase().includes(searchTerm)) return true;

      // Search in location
      if (booking.location?.toLowerCase().includes(searchTerm)) return true;

      // Search in booking reference
      if (booking.reference?.toLowerCase().includes(searchTerm)) return true;

      // Search in provider name
      if (booking.providerName?.toLowerCase().includes(searchTerm)) return true;

      // Search in amenities
      if (booking.amenities?.some(amenity =>
        amenity.toLowerCase().includes(searchTerm)
      )) return true;

      // Search in guest names
      if (booking.guests?.some(guest =>
        guest.toLowerCase().includes(searchTerm)
      )) return true;

      // Search in room details
      if (booking.roomDetails?.some(room =>
        room.name?.toLowerCase().includes(searchTerm) ||
        room.description?.toLowerCase().includes(searchTerm)
      )) return true;

      return false;
    } catch (error) {
      console.warn('Error searching booking:', booking.id, error);
      return false;
    }
  });
};

/**
 * Calculate booking statistics
 */
export const calculateBookingStats = (bookings: TransformedBookingItem[]) => {
  const totalBookings = bookings.length;
  const upcomingBookings = filterBookingsByStatus(bookings, "Upcoming").length;
  const completedBookings = filterBookingsByStatus(bookings, "Completed").length;
  const cancelledBookings = filterBookingsByStatus(bookings, "Cancelled").length;
  const totalValue = bookings.reduce((sum, booking) => sum + booking.price, 0);

  return {
    totalBookings,
    upcomingBookings,
    completedBookings,
    cancelledBookings,
    totalValue
  };
};
