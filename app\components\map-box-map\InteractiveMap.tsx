
'use client';

import 'mapbox-gl/dist/mapbox-gl.css';
import './InteractiveMap.css';
import { useState, useEffect, useCallback } from 'react';
import { Map, Marker, ViewState } from 'react-map-gl/mapbox';
import { MapPin } from 'lucide-react';
import { Hotel } from '@/models/hotel/list-page.model';
import HotelDetailMapPopup from './components/HotelDetailMapPopup';


type MapProps = {
  hotels: Hotel[];
  selectedHotelId?: number;
};

export default function InteractiveMap({ hotels, selectedHotelId }: MapProps) {
  const [viewport, setViewport] = useState<Partial<ViewState>>({
    latitude: 9.9312,
    longitude: 76.2673,
    zoom: 11,
  });

  const [selectedPin, setSelectedPin] = useState<Hotel | null>(null);

  const setCenterCoordinates = useCallback((selectedHotelId:number) => {
    if (selectedHotelId) {
      const mainHotel = hotels.find(h => h.hotelId === selectedHotelId);
      if (mainHotel) {
        setViewport({
          latitude: mainHotel.geoLocationInfo.lat,
          longitude: mainHotel.geoLocationInfo.lon,
          zoom: 13,
        });
      }
    }
  },[hotels]);

  // This effect centers the map on the selected hotel when in detail view.
  useEffect(() => {
      if(selectedHotelId){
        setCenterCoordinates(selectedHotelId);
      }else{
        setCenterCoordinates(hotels[0].hotelId);
      }
  }, [selectedHotelId,setCenterCoordinates,hotels]);

  const handleViewDetails = (hotelId: number) => {
    alert(`Navigate to details for hotel ID: ${hotelId}`);
  };

  return (
    <div className="map-container">
      <Map
        mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_GL_ACCESS_TOKEN}
        {...viewport}
        style={{ width: '100%', height: '100%' }}
        mapStyle="mapbox://styles/mapbox/streets-v11"
        onMove={(evt: { viewState: Partial<ViewState> }) => setViewport(evt.viewState)}
      >
        {hotels?.map(hotel => {
          const isSelectedOnDetailPage = hotel.hotelId === selectedHotelId;
          
          return (
            <Marker
              key={hotel.hotelId}
              latitude={hotel.geoLocationInfo.lat}
              longitude={hotel.geoLocationInfo.lon}
              anchor="bottom"
            >
              <div
                className={isSelectedOnDetailPage ? "selected-marker" : "price-marker"}
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  if (!selectedHotelId) {
                    setSelectedPin(hotel);
                  }
                }}
              >
                {isSelectedOnDetailPage ? (
                  <MapPin size={32} fill="#D9534F" color="white" />
                ) : (
                  `₹${hotel.fareDetail.totalPrice.toLocaleString('en-IN')}`
                )}
              </div>
            </Marker>
          );
        })}

        {/* Use the new HotelPopup component, but only when not in detail view */}
        {!selectedHotelId && selectedPin && (
          <HotelDetailMapPopup
            pin={selectedPin} 
            onClose={() => setSelectedPin(null)}
            onViewDetails={handleViewDetails}
          />
        )}
      </Map>
    </div>
  );
}